<?php
/**
 * test_api.php
 * Простой тест API новой архитектуры рекламы
 */

echo "<h1>🧪 Тест API новой архитектуры рекламы</h1>";

// Тест 1: Получение конфигурации
echo "<h2>1. Тест получения конфигурации</h2>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/ads-api.php?action=get_config');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response1 = curl_exec($ch);
curl_close($ch);

echo "<pre>Ответ: " . htmlspecialchars($response1) . "</pre>";

// Тест 2: Логирование клика
echo "<h2>2. Тест логирования клика</h2>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/ads-api.php?action=log_click');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'initData' => 'test_init_data_12345',
    'adType' => 'native_banner',
    'clickType' => 'button_click'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response2 = curl_exec($ch);
curl_close($ch);

echo "<pre>Ответ: " . htmlspecialchars($response2) . "</pre>";

// Тест 3: Получение лимитов
echo "<h2>3. Тест получения лимитов</h2>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/ads-api.php?action=get_limits');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'initData' => 'test_init_data_12345'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response3 = curl_exec($ch);
curl_close($ch);

echo "<pre>Ответ: " . htmlspecialchars($response3) . "</pre>";

// Тест 4: Запись просмотра рекламы
echo "<h2>4. Тест записи просмотра рекламы</h2>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/ads-api.php?action=record_view');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'initData' => 'test_init_data_12345',
    'adType' => 'native_banner'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response4 = curl_exec($ch);
curl_close($ch);

echo "<pre>Ответ: " . htmlspecialchars($response4) . "</pre>";

// Тест 5: Проверка старых API (совместимость)
echo "<h2>5. Тест совместимости со старыми API</h2>";
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://argun-defolt.loc/api/recordAdView.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
    'initData' => 'test_init_data_12345',
    'adType' => 'native_banner'
]));
curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response5 = curl_exec($ch);
curl_close($ch);

echo "<pre>Ответ старого API: " . htmlspecialchars($response5) . "</pre>";

// Проверка файлов
echo "<h2>6. Проверка файлов системы</h2>";
$files = [
    'js/ads-config.js',
    'js/ads-manager-unified.js', 
    'js/ads-state-manager.js',
    'api/ads-config.php',
    'api/ads-api.php'
];

foreach ($files as $file) {
    $exists = file_exists($file);
    $size = $exists ? filesize($file) : 0;
    echo "<div style='color: " . ($exists ? 'green' : 'red') . "'>";
    echo "📁 $file: " . ($exists ? "✅ существует ($size байт)" : "❌ не найден");
    echo "</div>";
}

// Проверка конфигурации
echo "<h2>7. Проверка конфигурации</h2>";
require_once 'api/ads-config.php';

$validation = AdsConfig::validate();
echo "<div style='color: " . ($validation['is_valid'] ? 'green' : 'red') . "'>";
echo "Конфигурация: " . ($validation['is_valid'] ? "✅ валидна" : "❌ содержит ошибки");
echo "</div>";

if (!$validation['is_valid']) {
    echo "<ul>";
    foreach ($validation['errors'] as $error) {
        echo "<li style='color: red;'>$error</li>";
    }
    echo "</ul>";
}

echo "<h2>8. Информация о типах рекламы</h2>";
$adTypes = AdsConfig::getAllAdTypes();
foreach ($adTypes as $type) {
    echo "<div style='border: 1px solid #ccc; margin: 10px; padding: 10px;'>";
    echo "<strong>{$type['id']}</strong><br>";
    echo "Награда: {$type['reward']} монет<br>";
    echo "Кнопка: {$type['button_id']}<br>";
    echo "Метод: {$type['method']}<br>";
    echo "Заголовок (RU): {$type['title']['ru']}<br>";
    echo "Заголовок (EN): {$type['title']['en']}<br>";
    echo "</div>";
}

echo "<h2>✅ Тестирование завершено</h2>";
echo "<p>Проверьте результаты выше. Все API должны возвращать JSON с success: true</p>";
?>
