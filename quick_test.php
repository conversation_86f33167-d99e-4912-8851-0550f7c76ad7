<?php
// Быстрый тест API
echo "🧪 Быстрый тест API\n\n";

// Тест 1: Прямой вызов API
echo "1. Тест прямого вызова API:\n";
$_REQUEST['action'] = 'get_config';
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['SCRIPT_NAME'] = 'api/ads-api.php'; // Имитируем прямой вызов

ob_start();
include 'api/ads-api.php';
$result = ob_get_clean();

echo "Результат: " . $result . "\n\n";

// Тест 2: Проверка конфигурации
echo "2. Тест конфигурации:\n";
require_once 'api/ads-config.php';

$validation = AdsConfig::validate();
echo "Валидация: " . ($validation['is_valid'] ? "✅ OK" : "❌ Ошибки") . "\n";

if (!$validation['is_valid']) {
    foreach ($validation['errors'] as $error) {
        echo "  - $error\n";
    }
}

$config = AdsConfig::getJsConfig();
echo "Конфигурация получена: " . (empty($config) ? "❌ Пустая" : "✅ OK") . "\n";
echo "Типы рекламы: " . count($config['ad_types']) . "\n\n";

// Тест 3: Проверка переводов
echo "3. Тест переводов:\n";
$testCounts = [0, 1, 2, 5, 20];
foreach ($testCounts as $count) {
    $textRu = AdsConfig::getCounterText('ru', $count);
    $textEn = AdsConfig::getCounterText('en', $count);
    echo "  $count: RU='$textRu', EN='$textEn'\n";
}

echo "\n✅ Тест завершен\n";
?>
