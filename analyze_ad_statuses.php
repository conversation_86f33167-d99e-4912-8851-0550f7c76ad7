<?php
/**
 * Ана<PERSON>из статусов RichAds в логе ad_requests.log
 * Показывает все статусы с процентным содержанием
 */

$logFile = 'api/ad_requests.log';

if (!file_exists($logFile)) {
    echo "❌ Файл лога не найден: $logFile\n";
    exit(1);
}

echo "🔍 Анализ статусов RichAds в файле: $logFile\n";
echo "=" . str_repeat("=", 60) . "\n\n";

// Счетчики статусов
$statusCounts = [];
$totalRecords = 0;
$richAdsStatuses = [];

// Читаем файл построчно
$handle = fopen($logFile, 'r');
if ($handle) {
    while (($line = fgets($handle)) !== false) {
        $line = trim($line);
        if (empty($line)) continue;
        
        $data = json_decode($line, true);
        if (!$data || !isset($data['status'])) continue;
        
        $totalRecords++;
        $status = $data['status'];
        
        // Увеличиваем счетчик для этого статуса
        if (!isset($statusCounts[$status])) {
            $statusCounts[$status] = 0;
        }
        $statusCounts[$status]++;
        
        // Проверяем, является ли это статусом от RichAds
        if (in_array($status, ['request', 'success', 'empty', 'error'])) {
            if (!isset($richAdsStatuses[$status])) {
                $richAdsStatuses[$status] = 0;
            }
            $richAdsStatuses[$status]++;
        }
    }
    fclose($handle);
}

// Сортируем статусы по количеству (по убыванию)
arsort($statusCounts);

echo "📊 ОБЩАЯ СТАТИСТИКА ВСЕХ СТАТУСОВ:\n";
echo "-" . str_repeat("-", 50) . "\n";
printf("%-25s | %-8s | %-10s\n", "Статус", "Количество", "Процент");
echo "-" . str_repeat("-", 50) . "\n";

foreach ($statusCounts as $status => $count) {
    $percentage = ($count / $totalRecords) * 100;
    printf("%-25s | %-8d | %6.2f%%\n", $status, $count, $percentage);
}

echo "\n📈 СТАТУСЫ ОТ RICHADDS (request, success, empty, error):\n";
echo "-" . str_repeat("-", 50) . "\n";

if (!empty($richAdsStatuses)) {
    $totalRichAds = array_sum($richAdsStatuses);
    
    printf("%-25s | %-8s | %-10s\n", "RichAds Статус", "Количество", "Процент");
    echo "-" . str_repeat("-", 50) . "\n";
    
    // Сортируем RichAds статусы
    arsort($richAdsStatuses);
    
    foreach ($richAdsStatuses as $status => $count) {
        $percentage = ($count / $totalRichAds) * 100;
        printf("%-25s | %-8d | %6.2f%%\n", $status, $count, $percentage);
    }
    
    echo "\n🎯 АНАЛИЗ ЭФФЕКТИВНОСТИ RICHADDS:\n";
    echo "-" . str_repeat("-", 40) . "\n";
    
    $successRate = isset($richAdsStatuses['success']) ? 
        ($richAdsStatuses['success'] / $totalRichAds) * 100 : 0;
    $errorRate = isset($richAdsStatuses['error']) ? 
        ($richAdsStatuses['error'] / $totalRichAds) * 100 : 0;
    $requestRate = isset($richAdsStatuses['request']) ? 
        ($richAdsStatuses['request'] / $totalRichAds) * 100 : 0;
    
    echo "✅ Успешные показы: " . sprintf("%.2f%%", $successRate) . "\n";
    echo "❌ Ошибки: " . sprintf("%.2f%%", $errorRate) . "\n";
    echo "📤 Запросы: " . sprintf("%.2f%%", $requestRate) . "\n";
    
    if (isset($richAdsStatuses['success']) && isset($richAdsStatuses['request'])) {
        $conversionRate = ($richAdsStatuses['success'] / $richAdsStatuses['request']) * 100;
        echo "🎯 Конверсия (success/request): " . sprintf("%.2f%%", $conversionRate) . "\n";
    }
    
} else {
    echo "❌ Статусы от RichAds не найдены в логе\n";
}

echo "\n📋 ИТОГОВАЯ ИНФОРМАЦИЯ:\n";
echo "-" . str_repeat("-", 30) . "\n";
echo "📄 Всего записей в логе: $totalRecords\n";
echo "📊 Уникальных статусов: " . count($statusCounts) . "\n";
echo "🎯 Записей от RichAds: " . array_sum($richAdsStatuses) . "\n";

// Показываем последние записи для примера
echo "\n🔍 ПОСЛЕДНИЕ 5 ЗАПИСЕЙ ИЗ ЛОГА:\n";
echo "-" . str_repeat("-", 60) . "\n";

$handle = fopen($logFile, 'r');
$lines = [];
if ($handle) {
    while (($line = fgets($handle)) !== false) {
        $lines[] = trim($line);
    }
    fclose($handle);
}

$lastLines = array_slice($lines, -5);
foreach ($lastLines as $line) {
    if (empty($line)) continue;
    $data = json_decode($line, true);
    if ($data) {
        echo sprintf("[%s] %s - %s\n", 
            $data['date'] ?? 'N/A', 
            $data['status'] ?? 'N/A',
            $data['ad_type'] ?? 'N/A'
        );
    }
}

// Анализ последовательности request -> success
echo "\n🔄 АНАЛИЗ ПОСЛЕДОВАТЕЛЬНОСТИ REQUEST -> SUCCESS:\n";
echo "-" . str_repeat("-", 60) . "\n";

$handle = fopen($logFile, 'r');
$requestSuccessPairs = [];
$pendingRequests = [];

if ($handle) {
    while (($line = fgets($handle)) !== false) {
        $line = trim($line);
        if (empty($line)) continue;

        $data = json_decode($line, true);
        if (!$data) continue;

        $userId = $data['user_id'] ?? 'unknown';
        $adType = $data['ad_type'] ?? 'unknown';
        $status = $data['status'] ?? 'unknown';
        $timestamp = $data['timestamp'] ?? 0;

        $key = $userId . '_' . $adType;

        if ($status === 'request') {
            $pendingRequests[$key] = $timestamp;
        } elseif ($status === 'success' && isset($pendingRequests[$key])) {
            $requestTime = $pendingRequests[$key];
            $successTime = $timestamp;
            $duration = $successTime - $requestTime;

            $requestSuccessPairs[] = [
                'user_id' => $userId,
                'ad_type' => $adType,
                'request_time' => $requestTime,
                'success_time' => $successTime,
                'duration' => $duration
            ];

            unset($pendingRequests[$key]);
        }
    }
    fclose($handle);
}

if (!empty($requestSuccessPairs)) {
    $totalPairs = count($requestSuccessPairs);
    $durations = array_column($requestSuccessPairs, 'duration');
    $avgDuration = array_sum($durations) / count($durations);
    $minDuration = min($durations);
    $maxDuration = max($durations);

    echo "✅ Найдено пар request->success: $totalPairs\n";
    echo "⏱️ Среднее время обработки: " . round($avgDuration, 2) . " сек\n";
    echo "⚡ Минимальное время: $minDuration сек\n";
    echo "🐌 Максимальное время: $maxDuration сек\n";

    // Показываем последние 5 пар
    echo "\n📋 ПОСЛЕДНИЕ 5 ПАР REQUEST->SUCCESS:\n";
    $lastPairs = array_slice($requestSuccessPairs, -5);
    foreach ($lastPairs as $pair) {
        echo sprintf("User %s, %s: %s сек\n",
            $pair['user_id'],
            $pair['ad_type'],
            $pair['duration']
        );
    }
} else {
    echo "❌ Пары request->success не найдены\n";
}

echo "\n🔍 НЕЗАВЕРШЕННЫЕ ЗАПРОСЫ (request без success):\n";
echo "-" . str_repeat("-", 50) . "\n";
if (!empty($pendingRequests)) {
    echo "⚠️ Найдено незавершенных запросов: " . count($pendingRequests) . "\n";
    $count = 0;
    foreach ($pendingRequests as $key => $timestamp) {
        if ($count >= 5) break;
        list($userId, $adType) = explode('_', $key, 2);
        $timeAgo = time() - $timestamp;
        echo sprintf("User %s, %s: %d сек назад\n", $userId, $adType, $timeAgo);
        $count++;
    }
    if (count($pendingRequests) > 5) {
        echo "... и еще " . (count($pendingRequests) - 5) . " запросов\n";
    }
} else {
    echo "✅ Все запросы завершены успешно\n";
}

echo "\n✅ Анализ завершен!\n";
?>
