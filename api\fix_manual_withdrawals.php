<?php
/**
 * Исправление выплат с manual_ ID и null payout_id
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';
require_once 'functions.php';

header('Content-Type: text/html; charset=utf-8');

echo "<h1>🔧 Исправление выплат с manual_ ID</h1>\n";

// Загружаем данные пользователей
$userData = loadUserData();
if (!$userData) {
    echo "<p style='color: red;'>❌ Не удалось загрузить данные пользователей</p>\n";
    exit;
}

$manualWithdrawals = [];
$totalChecked = 0;

// Находим все выплаты с manual_ ID
foreach ($userData as $userId => $user) {
    if (!isset($user['withdrawals']) || !is_array($user['withdrawals'])) {
        continue;
    }
    
    foreach ($user['withdrawals'] as $index => $withdrawal) {
        $totalChecked++;
        $id = $withdrawal['id'] ?? '';
        $payoutId = $withdrawal['payout_id'] ?? null;
        
        // Ищем выплаты с manual_ ID и null payout_id
        if (strpos($id, 'manual_') === 0 && $payoutId === null) {
            $manualWithdrawals[] = [
                'user_id' => $userId,
                'index' => $index,
                'withdrawal' => $withdrawal,
                'user_info' => [
                    'first_name' => $user['first_name'] ?? '',
                    'last_name' => $user['last_name'] ?? '',
                    'username' => $user['username'] ?? ''
                ]
            ];
        }
    }
}

echo "<h2>📊 Статистика</h2>\n";
echo "<p>Всего проверено выплат: <strong>{$totalChecked}</strong></p>\n";
echo "<p>Найдено выплат с manual_ ID: <strong>" . count($manualWithdrawals) . "</strong></p>\n";

if (empty($manualWithdrawals)) {
    echo "<p style='color: green;'>✅ Все выплаты в порядке!</p>\n";
    exit;
}

echo "<h2>🔍 Найденные проблемные выплаты</h2>\n";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr style='background: #f0f0f0;'>\n";
echo "<th>User ID</th><th>Имя</th><th>ID выплаты</th><th>Сумма</th><th>Валюта</th><th>Статус</th><th>Дата</th><th>Действие</th>\n";
echo "</tr>\n";

foreach ($manualWithdrawals as $item) {
    $userId = $item['user_id'];
    $withdrawal = $item['withdrawal'];
    $userInfo = $item['user_info'];
    
    $fullName = trim($userInfo['first_name'] . ' ' . $userInfo['last_name']);
    $username = $userInfo['username'] ? '@' . $userInfo['username'] : '';
    $displayName = $fullName ?: $username ?: 'Без имени';
    
    echo "<tr>\n";
    echo "<td>{$userId}</td>\n";
    echo "<td>{$displayName}</td>\n";
    echo "<td>{$withdrawal['id']}</td>\n";
    echo "<td>{$withdrawal['coins_amount']} монет ({$withdrawal['usd_amount']} USD)</td>\n";
    echo "<td>{$withdrawal['currency']}</td>\n";
    echo "<td>{$withdrawal['status']}</td>\n";
    echo "<td>{$withdrawal['created_at']}</td>\n";
    echo "<td><button onclick=\"fixWithdrawal('{$userId}', '{$item['index']}')\">Исправить</button></td>\n";
    echo "</tr>\n";
}

echo "</table>\n";

// Если есть параметр для исправления
if (isset($_GET['fix_user']) && isset($_GET['fix_index'])) {
    $fixUserId = $_GET['fix_user'];
    $fixIndex = (int)$_GET['fix_index'];
    
    echo "<h2>🔧 Исправление выплаты</h2>\n";
    
    if (!isset($userData[$fixUserId]['withdrawals'][$fixIndex])) {
        echo "<p style='color: red;'>❌ Выплата не найдена</p>\n";
        exit;
    }
    
    $withdrawal = &$userData[$fixUserId]['withdrawals'][$fixIndex];
    
    try {
        // Создаем экземпляр API
        $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
        
        echo "<p>🚀 Создаем выплату в NOWPayments...</p>\n";
        
        // Создаем выплату в NOWPayments
        $result = $api->createSinglePayout(
            $withdrawal['address'],
            $withdrawal['currency'],
            $withdrawal['crypto_amount_gross'] ?? $withdrawal['crypto_amount']
        );
        
        if ($result && !isset($result['error']) && isset($result['id'])) {
            // Обновляем данные выплаты
            $withdrawal['payout_id'] = $result['id'];
            $withdrawal['status'] = $result['status'] ?? 'waiting';
            $withdrawal['updated_at'] = date('Y-m-d H:i:s');
            $withdrawal['nowpayments_created'] = true;
            $withdrawal['original_id'] = $withdrawal['id'];
            $withdrawal['id'] = $result['id'];
            
            // Сохраняем данные
            if (saveUserData($userData)) {
                echo "<p style='color: green;'>✅ Выплата успешно создана в NOWPayments!</p>\n";
                echo "<p><strong>Новый payout_id:</strong> {$result['id']}</p>\n";
                echo "<p><strong>Статус:</strong> {$withdrawal['status']}</p>\n";
                
                // Перезагружаем страницу для обновления списка
                echo "<script>setTimeout(() => location.reload(), 2000);</script>\n";
            } else {
                echo "<p style='color: red;'>❌ Ошибка сохранения данных</p>\n";
            }
        } else {
            $errorMsg = $result['message'] ?? 'Неизвестная ошибка';
            echo "<p style='color: red;'>❌ Ошибка создания выплаты: {$errorMsg}</p>\n";
            
            if (isset($result['details'])) {
                echo "<pre>" . json_encode($result['details'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>\n";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Исключение: " . $e->getMessage() . "</p>\n";
    }
}
?>

<script>
function fixWithdrawal(userId, index) {
    if (confirm('Создать выплату в NOWPayments для пользователя ' + userId + '?')) {
        window.location.href = '?fix_user=' + userId + '&fix_index=' + index;
    }
}
</script>

<style>
table { margin: 20px 0; }
th, td { padding: 8px; text-align: left; }
button { padding: 5px 10px; background: #007cba; color: white; border: none; cursor: pointer; }
button:hover { background: #005a87; }
</style>
