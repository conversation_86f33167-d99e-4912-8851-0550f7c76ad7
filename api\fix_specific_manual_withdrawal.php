<?php
/**
 * Быстрое исправление конкретной выплаты с manual_ ID
 */

require_once 'config.php';
require_once 'NOWPaymentsAPI.php';
require_once 'functions.php';

// Параметры для исправления
$targetUserId = '7947418432'; // ID пользователя Qulisa
$targetWithdrawalId = 'manual_1751799278'; // ID проблемной выплаты

echo "🔧 ИСПРАВЛЕНИЕ ВЫПЛАТЫ {$targetWithdrawalId} ДЛЯ ПОЛЬЗОВАТЕЛЯ {$targetUserId}\n\n";

// Загружаем данные пользователей
$userData = loadUserData();
if (!$userData) {
    echo "❌ Не удалось загрузить данные пользователей\n";
    exit;
}

// Проверяем существование пользователя
if (!isset($userData[$targetUserId])) {
    echo "❌ Пользователь {$targetUserId} не найден\n";
    exit;
}

// Ищем выплату
$withdrawalIndex = null;
$withdrawal = null;

if (isset($userData[$targetUserId]['withdrawals'])) {
    foreach ($userData[$targetUserId]['withdrawals'] as $index => $w) {
        if ($w['id'] === $targetWithdrawalId) {
            $withdrawalIndex = $index;
            $withdrawal = $w;
            break;
        }
    }
}

if ($withdrawal === null) {
    echo "❌ Выплата {$targetWithdrawalId} не найдена\n";
    exit;
}

echo "✅ Выплата найдена:\n";
echo "   ID: {$withdrawal['id']}\n";
echo "   Payout ID: " . ($withdrawal['payout_id'] ?? 'null') . "\n";
echo "   Статус: {$withdrawal['status']}\n";
echo "   Сумма: {$withdrawal['coins_amount']} монет ({$withdrawal['usd_amount']} USD)\n";
echo "   Валюта: {$withdrawal['currency']}\n";
echo "   Адрес: {$withdrawal['address']}\n";
echo "   Крипто сумма: " . ($withdrawal['crypto_amount_gross'] ?? $withdrawal['crypto_amount']) . "\n\n";

// Проверяем, нужно ли исправление
if ($withdrawal['payout_id'] !== null) {
    echo "✅ У выплаты уже есть payout_id: {$withdrawal['payout_id']}\n";
    echo "Проверяем статус в NOWPayments...\n";
    
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    $status = $api->getPayoutStatus($withdrawal['payout_id']);
    
    if ($status) {
        echo "📊 Статус в NOWPayments: {$status}\n";
    } else {
        echo "❌ Не удалось получить статус из NOWPayments\n";
    }
    exit;
}

echo "🚀 Создаем выплату в NOWPayments...\n";

try {
    // Создаем экземпляр API
    $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);
    
    // Определяем сумму для отправки
    $cryptoAmount = $withdrawal['crypto_amount_gross'] ?? $withdrawal['crypto_amount'];
    
    echo "   Отправляем: {$cryptoAmount} {$withdrawal['currency']}\n";
    echo "   На адрес: {$withdrawal['address']}\n\n";
    
    // Создаем выплату в NOWPayments
    $result = $api->createSinglePayout(
        $withdrawal['address'],
        $withdrawal['currency'],
        $cryptoAmount
    );
    
    if ($result && !isset($result['error']) && isset($result['id'])) {
        echo "✅ Выплата успешно создана в NOWPayments!\n";
        echo "   Новый payout_id: {$result['id']}\n";
        echo "   Статус: " . ($result['status'] ?? 'unknown') . "\n\n";
        
        // Обновляем данные выплаты
        $userData[$targetUserId]['withdrawals'][$withdrawalIndex]['payout_id'] = $result['id'];
        $userData[$targetUserId]['withdrawals'][$withdrawalIndex]['status'] = $result['status'] ?? 'waiting';
        $userData[$targetUserId]['withdrawals'][$withdrawalIndex]['updated_at'] = date('Y-m-d H:i:s');
        $userData[$targetUserId]['withdrawals'][$withdrawalIndex]['nowpayments_created'] = true;
        $userData[$targetUserId]['withdrawals'][$withdrawalIndex]['original_manual_id'] = $targetWithdrawalId;
        
        // Обновляем ID на реальный от NOWPayments
        $userData[$targetUserId]['withdrawals'][$withdrawalIndex]['id'] = $result['id'];
        
        // Сохраняем данные
        if (saveUserData($userData)) {
            echo "✅ Данные успешно сохранены!\n";
            echo "\n🎉 ИСПРАВЛЕНИЕ ЗАВЕРШЕНО!\n";
            echo "Теперь система сможет отслеживать статус этой выплаты.\n";
        } else {
            echo "❌ Ошибка сохранения данных\n";
        }
        
    } else {
        echo "❌ Ошибка создания выплаты в NOWPayments:\n";
        
        if (isset($result['error'])) {
            echo "   Ошибка: {$result['message']}\n";
            echo "   Код: " . ($result['code'] ?? 'unknown') . "\n";
        }
        
        if (isset($result['details'])) {
            echo "   Детали:\n";
            echo json_encode($result['details'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
        }
        
        // Если ошибка связана с недостаточным балансом
        if (isset($result['code']) && $result['code'] === 'BAD_CREATE_WITHDRAWAL_REQUEST') {
            echo "\n💡 РЕКОМЕНДАЦИИ:\n";
            echo "1. Проверьте баланс в панели NOWPayments\n";
            echo "2. Убедитесь, что у вас достаточно средств в валюте {$withdrawal['currency']}\n";
            echo "3. Попробуйте создать выплату позже\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Исключение: " . $e->getMessage() . "\n";
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "Скрипт завершен.\n";
?>
