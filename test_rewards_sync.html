<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест синхронизации наград</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        input[type="number"] {
            width: 80px;
            padding: 5px;
            margin: 0 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .reward-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .button-demo {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 8px;
            margin: 10px 0;
            display: block;
            width: 100%;
            text-align: left;
            cursor: pointer;
        }
        .button-demo:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔄 Тест синхронизации наград между админкой и приложением</h1>
    
    <div class="container">
        <!-- Левая панель - API тесты -->
        <div class="panel">
            <h3>📡 API тесты</h3>
            
            <div>
                <h4>Получение наград:</h4>
                <button class="test-button" onclick="getRewards()">Получить награды</button>
                <div id="get-result" class="result"></div>
            </div>
            
            <div>
                <h4>Изменение наград:</h4>
                <div>
                    <label>Native Banner: <input type="number" id="native_banner" value="15" min="1"></label>
                    <label>Interstitial: <input type="number" id="interstitial" value="20" min="1"></label>
                    <label>Rewarded Video: <input type="number" id="rewarded_video" value="5" min="1"></label>
                </div>
                <br>
                <button class="test-button" onclick="saveRewards()">Сохранить награды</button>
                <div id="save-result" class="result"></div>
            </div>
            
            <div>
                <h4>Автообновление:</h4>
                <button class="test-button" onclick="startAutoRefresh()">Запустить автообновление (5 сек)</button>
                <button class="test-button" onclick="stopAutoRefresh()">Остановить автообновление</button>
                <div id="auto-refresh-status" class="result"></div>
            </div>
        </div>
        
        <!-- Правая панель - Демо кнопок -->
        <div class="panel">
            <h3>🏆 Демо кнопок с наградами</h3>
            
            <button class="button-demo" id="demo-native-banner">
                🔗 Открыть ссылку
                <span class="reward-badge" id="badge-native-banner">+10</span>
            </button>
            
            <button class="button-demo" id="demo-rewarded-video">
                📹 Смотреть видео
                <span class="reward-badge" id="badge-rewarded-video">+1</span>
            </button>
            
            <button class="button-demo" id="demo-interstitial">
                🎯 Кликнуть по баннеру
                <span class="reward-badge" id="badge-interstitial">+10</span>
            </button>
            
            <div>
                <h4>Управление badges:</h4>
                <button class="test-button" onclick="updateDemoBadges()">Обновить badges</button>
                <button class="test-button" onclick="resetDemoBadges()">Сбросить badges</button>
                <div id="badges-result" class="result"></div>
            </div>
        </div>
    </div>

    <script>
        let autoRefreshInterval = null;

        // Функция для отображения результатов
        function showResult(elementId, data, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }

        // Получение наград
        async function getRewards() {
            try {
                showResult('get-result', 'Загрузка...', 'info');
                
                const response = await fetch('api/get_ad_rewards.php');
                const data = await response.json();
                
                if (data.success) {
                    showResult('get-result', data, 'success');
                    updateDemoBadgesFromData(data.rewards);
                } else {
                    showResult('get-result', data, 'error');
                }
            } catch (error) {
                showResult('get-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Сохранение наград
        async function saveRewards() {
            try {
                showResult('save-result', 'Сохранение...', 'info');
                
                const data = {
                    action: 'change_settings',
                    ad_reward_native_banner: parseInt(document.getElementById('native_banner').value),
                    ad_reward_interstitial: parseInt(document.getElementById('interstitial').value),
                    ad_reward_rewarded_video: parseInt(document.getElementById('rewarded_video').value)
                };
                
                const response = await fetch('api/admin/save_settings.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('save-result', result, 'success');
                    // Автоматически обновляем badges после сохранения
                    setTimeout(getRewards, 500);
                } else {
                    showResult('save-result', result, 'error');
                }
            } catch (error) {
                showResult('save-result', `Ошибка: ${error.message}`, 'error');
            }
        }

        // Обновление демо badges из данных API
        function updateDemoBadgesFromData(rewards) {
            if (rewards.native_banner) {
                document.getElementById('badge-native-banner').textContent = `+${rewards.native_banner}`;
            }
            if (rewards.rewarded_video) {
                document.getElementById('badge-rewarded-video').textContent = `+${rewards.rewarded_video}`;
            }
            if (rewards.interstitial) {
                document.getElementById('badge-interstitial').textContent = `+${rewards.interstitial}`;
            }
            
            showResult('badges-result', `Badges обновлены: ${JSON.stringify(rewards)}`, 'success');
        }

        // Обновление демо badges
        function updateDemoBadges() {
            getRewards();
        }

        // Сброс демо badges
        function resetDemoBadges() {
            document.getElementById('badge-native-banner').textContent = '+10';
            document.getElementById('badge-rewarded-video').textContent = '+1';
            document.getElementById('badge-interstitial').textContent = '+10';
            showResult('badges-result', 'Badges сброшены к значениям по умолчанию', 'info');
        }

        // Запуск автообновления
        function startAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
            
            autoRefreshInterval = setInterval(() => {
                getRewards();
                showResult('auto-refresh-status', `Автообновление активно (${new Date().toLocaleTimeString()})`, 'info');
            }, 5000);
            
            showResult('auto-refresh-status', 'Автообновление запущено (каждые 5 секунд)', 'success');
        }

        // Остановка автообновления
        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                showResult('auto-refresh-status', 'Автообновление остановлено', 'info');
            }
        }

        // Автоматически получаем награды при загрузке
        window.addEventListener('load', () => {
            getRewards();
        });
    </script>
</body>
</html>
