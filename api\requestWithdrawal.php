<?php
/**
 * api/requestWithdrawal.php
 * API эндпоинт для запроса вывода средств через NOWPayments.
 */

// Включаем логирование
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');
error_reporting(E_ALL);

header('Content-Type: application/json');

// --- Подключение зависимостей с проверкой ---
if (!(@require_once __DIR__ . '/config.php')) {
    http_response_code(500);
    error_log('FATAL: config.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: CFG']);
    exit;
}
if (!(@require_once __DIR__ . '/validate_initdata.php')) {
    http_response_code(500);
    error_log('FATAL: validate_initdata.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: VID']);
    exit;
}
if (!(@require_once __DIR__ . '/db_mock.php')) {
    http_response_code(500);
    error_log('FATAL: db_mock.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: DBM']);
    exit;
}
if (!(@require_once __DIR__ . '/security.php')) {
    http_response_code(500);
    error_log('FATAL: security.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: SEC']);
    exit;
}
// --- Конец проверки зависимостей ---

// Подключаем класс для работы с NOWPayments API
if (!(@require_once __DIR__ . '/NOWPaymentsAPI.php')) {
    http_response_code(500);
    error_log('FATAL: NOWPaymentsAPI.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: NPAPI']);
    exit;
}

// Подключаем новый калькулятор комиссий
if (!(@require_once __DIR__ . '/FeeCalculator.php')) {
    http_response_code(500);
    error_log('FATAL: FeeCalculator.php not found in requestWithdrawal.php');
    echo json_encode(['error'=>'Ошибка сервера: CALC']);
    exit;
}

// 1. Получение и декодирование входных данных
$inputJSON = file_get_contents('php://input');
$input = json_decode($inputJSON, true);

if ($input === null || !isset($input['initData']) || empty($input['initData'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Нет данных']);
    exit;
}

if (!isset($input['amount']) || !is_numeric($input['amount']) || $input['amount'] <= 0) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Некорректная сумма']);
    exit;
}

// Проверяем адрес кошелька - может быть в разных полях
$cryptoAddress = null;
if (isset($input['crypto_address']) && !empty($input['crypto_address'])) {
    $cryptoAddress = trim($input['crypto_address']);
} elseif (isset($input['address']) && !empty($input['address'])) {
    $cryptoAddress = trim($input['address']);
} elseif (isset($input['wallet_address']) && !empty($input['wallet_address'])) {
    $cryptoAddress = trim($input['wallet_address']);
}

if (!$cryptoAddress) {
    error_log("requestWithdrawal ERROR: Отсутствует адрес кошелька");
    http_response_code(400);
    echo json_encode([
        'error' => 'Не указан адрес кошелька',
        'field' => 'crypto_address',
        'code' => 'MISSING_WALLET_ADDRESS'
    ]);
    exit;
}

// Дополнительная валидация адреса
if (strlen($cryptoAddress) < 10) {
    error_log("requestWithdrawal ERROR: Неверная длина адреса: " . $cryptoAddress);
    http_response_code(400);
    echo json_encode([
        'error' => 'Адрес кошелька слишком короткий',
        'field' => 'crypto_address',
        'code' => 'INVALID_WALLET_ADDRESS'
    ]);
    exit;
}

// Проверяем криптовалюту - может быть в разных полях
$cryptoCurrency = null;
if (isset($input['crypto_currency']) && !empty($input['crypto_currency'])) {
    $cryptoCurrency = strtolower(trim($input['crypto_currency']));
} elseif (isset($input['currency']) && !empty($input['currency'])) {
    $cryptoCurrency = strtolower(trim($input['currency']));
}

if (!$cryptoCurrency) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Не указана криптовалюта']);
    exit;
}

$initData = $input['initData'];
$amount = intval($input['amount']);

// Получаем точную сумму криптовалюты если она передана
$exactCryptoAmount = isset($input['crypto_amount']) ? floatval($input['crypto_amount']) : null;

// Валидация адреса кошелька в зависимости от выбранной валюты
$isValidAddress = false;
switch ($cryptoCurrency) {
    case 'usdttrc20':
    case 'trx':
        // Адрес TRC20 обычно начинается с T и имеет длину 34 символа
        $isValidAddress = preg_match('/^T[a-zA-Z0-9]{33}$/', $cryptoAddress);
        break;
    case 'btc':
        // Адрес Bitcoin обычно начинается с 1, 3 или bc1 и имеет длину от 26 до 35 символов
        $isValidAddress = preg_match('/^(1|3|bc1)[a-zA-Z0-9]{25,58}$/', $cryptoAddress);
        break;
    case 'eth':
        // Адрес Ethereum начинается с 0x и имеет длину 42 символа
        $isValidAddress = preg_match('/^0x[a-fA-F0-9]{40}$/', $cryptoAddress);
        break;
    case 'bnb':
        // Адрес BNB может начинаться с bnb или 0x
        $isValidAddress = preg_match('/^(bnb1|0x)[a-zA-Z0-9]{38,40}$/', $cryptoAddress);
        break;
    default:
        // Общая проверка - не менее 20 символов
        $isValidAddress = strlen($cryptoAddress) >= 20;
}

if (!$isValidAddress) {
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка запроса: Неверный формат адреса кошелька для выбранной криптовалюты']);
    exit;
}

error_log("requestWithdrawal INFO: Получен запрос на вывод {$amount} монет в {$cryptoCurrency} на адрес {$cryptoAddress}");

// 2. Валидация initData с подробным логированием
error_log("requestWithdrawal DEBUG: Validating initData: " . substr($initData, 0, 100) . "...");
$validatedData = validateTelegramInitData($initData);
if ($validatedData === false) {
    error_log("requestWithdrawal ERROR: Invalid initData: " . $initData);
    http_response_code(403);
    echo json_encode([
        'error' => 'Ошибка авторизации',
        'details' => 'Не удалось верифицировать данные Telegram'
    ]);
    exit;
}
$userId = intval($validatedData['user']['id']);
error_log("requestWithdrawal INFO: initData валидирован для user {$userId}");

// 3. Загрузка данных пользователя
$userData = loadUserData();
if (!is_array($userData)) {
    error_log("requestWithdrawal ERROR: loadUserData вернул не массив");
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера: LD1']);
    exit;
}

// 4. Проверка безопасности и баланса пользователя
if (!isset($userData[$userId]) || !isset($userData[$userId]['balance'])) {
    error_log("requestWithdrawal ERROR: Пользователь {$userId} не найден или нет баланса");
    http_response_code(404);
    echo json_encode(['error' => 'Ошибка: Пользователь не найден']);
    exit;
}

// Проверяем, не заблокирован ли пользователь
if (isset($userData[$userId]['blocked']) && $userData[$userId]['blocked']) {
    error_log("requestWithdrawal WARNING: Попытка вывода средств заблокированным пользователем {$userId}");
    http_response_code(403);
    echo json_encode(['error' => 'Ваш аккаунт заблокирован из-за подозрительной активности']);
    exit;
}

// ЛИМИТЫ НА ВЫВОДЫ ОТКЛЮЧЕНЫ - пропускаем проверку лимитов
// Проверяем лимит выводов средств (ОТКЛЮЧЕНО)
/*
if (!checkWithdrawalLimit($userId, $userData)) {
    error_log("requestWithdrawal WARNING: Превышен лимит выводов средств для пользователя {$userId}");

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'withdrawal_limit_exceeded');

    http_response_code(429);
    echo json_encode(['error' => 'Превышен лимит выводов средств. Попробуйте позже']);
    exit;
}
*/

// Проверяем подозрительную активность
if (!checkSuspiciousActivity($userId, $userData)) {
    error_log("requestWithdrawal WARNING: Обнаружена подозрительная активность пользователя {$userId}");
    http_response_code(403);
    echo json_encode(['error' => 'Вывод средств временно ограничен из-за подозрительной активности']);
    exit;
}

$userBalance = $userData[$userId]['balance'];

// Проверяем минимальный баланс для доступа к выводу (снижен до 1 монеты)
if ($userBalance < MIN_BALANCE_FOR_WITHDRAWAL) {
    error_log("requestWithdrawal ERROR: Недостаточный баланс для доступа к выводу у пользователя {$userId}. Баланс: {$userBalance}, Требуется: " . MIN_BALANCE_FOR_WITHDRAWAL);
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка: Для вывода средств необходимо иметь минимум ' . MIN_BALANCE_FOR_WITHDRAWAL . ' монет на балансе']);
    exit;
}

// Проверяем баланс с использованием функции безопасности
if (!verifyBalance($userId, $amount, $userData)) {
    error_log("requestWithdrawal ERROR: Проверка баланса не пройдена для пользователя {$userId}. Запрошено: {$amount}, Доступно: {$userBalance}");

    // Увеличиваем счетчик подозрительной активности
    incrementSuspiciousActivity($userId, $userData, 'invalid_withdrawal_amount');

    http_response_code(400);
    echo json_encode(['error' => 'Ошибка: Недостаточно средств или сумма превышает доступную для вывода']);
    exit;
}

// 5. Проверка минимальной суммы вывода (только если установлена)
if (MIN_WITHDRAWAL_AMOUNT > 0 && $amount < MIN_WITHDRAWAL_AMOUNT) {
    error_log("requestWithdrawal ERROR: Сумма вывода меньше минимальной. Запрошено: {$amount}, Минимум: " . MIN_WITHDRAWAL_AMOUNT);
    http_response_code(400);
    echo json_encode(['error' => 'Ошибка: Минимальная сумма для вывода - ' . MIN_WITHDRAWAL_AMOUNT . ' монет']);
    exit;
}

// Логируем событие запроса на вывод средств
logAuditEvent('withdrawal_request', $userId, [
    'amount' => $amount,
    'currency' => $cryptoCurrency,
    'address' => $cryptoAddress
]);

// 6. Расчет суммы с улучшенным логированием
error_log("requestWithdrawal DEBUG: Calculating crypto amount for {$amount} coins -> {$cryptoCurrency}");
error_log("requestWithdrawal DEBUG: User balance: {$userBalance}, Min withdrawal: " . MIN_WITHDRAWAL_AMOUNT);

try {
    error_log("requestWithdrawal DEBUG: Initializing FeeCalculator");
    $calculator = FeeCalculator::getInstance();
    $calculationResult = $calculator->calculateWithdrawalAmount($amount, $cryptoCurrency);
    error_log("requestWithdrawal DEBUG: Calculation result: " . json_encode($calculationResult));

    if (!$calculationResult['success']) {
        error_log("requestWithdrawal ERROR: Ошибка расчета: " . $calculationResult['error']);
        http_response_code(400);
        echo json_encode(['error' => 'Ошибка расчета: ' . $calculationResult['error']]);
        exit;
    }

    // Получаем рассчитанные суммы
    $cryptoAmountForUser = $calculationResult['crypto_amount']; // То что получит пользователь
    $cryptoAmountForNowPayments = $calculationResult['crypto_amount_for_nowpayments'] ?? $calculationResult['crypto_amount_gross']; // То что отправляем в NOWPayments
    $usdAmount = $calculationResult['usd_amount'];
    $nowPaymentsFee = $calculationResult['nowpayments_fee'];

    error_log("requestWithdrawal INFO: Расчет завершен - Пользователь получит: {$cryptoAmountForUser} {$cryptoCurrency}, NOWPayments получит: {$cryptoAmountForNowPayments} {$cryptoCurrency}, Комиссия: {$nowPaymentsFee} {$cryptoCurrency}");

    // Если передана точная сумма из фронтенда, проверяем соответствие
    if ($exactCryptoAmount !== null && $exactCryptoAmount > 0) {
        $difference = abs($exactCryptoAmount - $cryptoAmountForNowPayments);
        if ($difference > 0.00001) { // Допускаем небольшую погрешность
            error_log("requestWithdrawal WARNING: Расхождение между фронтендом ({$exactCryptoAmount}) и бэкендом ({$cryptoAmountForNowPayments}) для {$cryptoCurrency}");
        }
        // Используем сумму из бэкенда как более точную
    }

} catch (Exception $e) {
    error_log("requestWithdrawal ERROR: Исключение в FeeCalculator: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка сервера при расчете комиссий']);
    exit;
}

// 7. Списываем монеты с баланса СРАЗУ при создании заявки
$userData[$userId]['balance'] -= $amount;
error_log("requestWithdrawal INFO: Списано {$amount} монет с баланса пользователя {$userId}. Новый баланс: {$userData[$userId]['balance']}");

// 8. Создание запроса на вывод через NOWPayments API
try {
    // Получаем экземпляр калькулятора
    $calculator = FeeCalculator::getInstance();
    
    // ТОЧНЫЙ РАСЧЕТ: Гарантируем что пользователь получит именно ту сумму, что в калькуляторе
    if ($exactCryptoAmount > 0) {
        // Используем алгоритм идеального расчёта для точного соответствия
        $perfectResult = $calculator->calculatePerfectNOWPaymentsAmount($exactCryptoAmount, $cryptoCurrency);
        
        if ($perfectResult['success']) {
            $cryptoAmountForNowPayments = $perfectResult['nowpayments_amount'];
            $cryptoAmountForUser = $perfectResult['user_will_receive'];
            $nowPaymentsFee = $perfectResult['actual_fee'];
            
            error_log("requestWithdrawal: Точный расчет - пользователь получит {$cryptoAmountForUser} {$cryptoCurrency}, NOWPayments получит {$cryptoAmountForNowPayments}");
        } else {
            error_log("requestWithdrawal WARNING: Не удалось выполнить точный расчет: " . $perfectResult['error']);
        }
    }
    
    // Создаем платеж в NOWPayments
    $result = createWithdrawalRequest($userId, $usdAmount, $cryptoCurrency, $cryptoAddress, $cryptoAmountForNowPayments);

    // 8. Сохранение информации о выводе (без списания баланса)
    if (!isset($userData[$userId]['withdrawals'])) {
        $userData[$userId]['withdrawals'] = [];
    }

    // Определяем начальный статус на основе ответа NOWPayments
    $initialStatus = 'waiting'; // По умолчанию "ожидание" (как в NOWPayments)
    if (isset($result['status'])) {
        $initialStatus = strtolower($result['status']);
    }

    // Добавляем информацию о выводе с правильным статусом
    $withdrawalData = [
        'id' => $result['id'] ?? ('manual_' . time()),
        'payout_id' => $result['id'] ?? null,
        'status' => $initialStatus,
        'coins_amount' => $amount,
        'usd_amount' => $usdAmount,
        'crypto_amount' => $cryptoAmountForUser, // То что получит пользователь
        'crypto_amount_gross' => $cryptoAmountForNowPayments, // То что отправили в NOWPayments
        'nowpayments_fee' => $nowPaymentsFee, // Комиссия NOWPayments
        'currency' => $cryptoCurrency,
        'address' => $cryptoAddress,
        'wallet_address' => $cryptoAddress,
        'timestamp' => time(),
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s'),
        'calculation_method' => 'fee_calculator_v2'
    ];

    $userData[$userId]['withdrawals'][] = $withdrawalData;
    error_log("requestWithdrawal INFO: Добавлена выплата в массив пользователя {$userId}. Статус: {$initialStatus}. Всего выплат: " . count($userData[$userId]['withdrawals']));

    // 9. Сохранение данных пользователя (с обновленным балансом и новой выплатой)
    if (!saveUserData($userData)) {
        // Если сохранение не удалось, возвращаем монеты на баланс
        $userData[$userId]['balance'] += $amount;
        error_log("requestWithdrawal ERROR: Не удалось сохранить данные пользователя {$userId} после создания выплаты, монеты возвращены");
        http_response_code(500);
        echo json_encode(['error' => 'Ошибка сервера: Не удалось сохранить запись о выплате']);
        exit;
    }
    error_log("requestWithdrawal INFO: Данные пользователя {$userId} успешно сохранены с новой выплатой. Новый баланс: {$userData[$userId]['balance']}");

    // 10. Логируем создание запроса на вывод
    logAuditEvent('withdrawal_requested', $userId, $withdrawalData);

    // 12. Успешный ответ
    $responseMessage = 'Запрос на вывод успешно создан! Монеты списаны с баланса. Статус: ' . getStatusText($initialStatus) . '.';

    // Добавляем информацию об автоконвертации, если она была использована
    if (isset($result['conversion_info'])) {
        $responseMessage .= '. ' . $result['conversion_info'];
    }

    http_response_code(200);
    echo json_encode([
        'success' => true,
        'message' => $responseMessage,
        'withdrawal_id' => $result['id'] ?? null,
        'new_balance' => $userData[$userId]['balance'],
        'withdrawal_data' => $withdrawalData,
        'auto_conversion' => $result['auto_conversion'] ?? null,
        'internal_conversion' => $result['internal_conversion'] ?? null
    ]);
    error_log("requestWithdrawal INFO: Успешно создан запрос на вывод для пользователя {$userId}. Новый баланс: {$userData[$userId]['balance']}");

} catch (Exception $e) {
    error_log("requestWithdrawal ERROR: Исключение при создании запроса на вывод: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Ошибка: ' . $e->getMessage()]);
    exit;
}

/**
 * Создает запрос на вывод средств через NOWPayments API
 *
 * @param int $userId ID пользователя
 * @param float $amount Сумма в USD
 * @param string $currency Криптовалюта (BTC, ETH, и т.д.)
 * @param string $address Адрес кошелька
 * @param float|null $exactCryptoAmount Точная сумма криптовалюты (если передана из авторасчета)
 * @return array|false Результат запроса или false в случае ошибки
 */
function createWithdrawalRequest($userId, $amount, $currency, $address, $exactCryptoAmount = null) {
    try {
        error_log("createWithdrawalRequest INFO: Начало создания запроса на вывод для пользователя {$userId}");
        error_log("createWithdrawalRequest INFO: Параметры - Amount: {$amount} USD, Currency: {$currency}, Address: {$address}");

        // Создаем экземпляр API клиента
        $api = new NOWPaymentsAPI(NOWPAYMENTS_API_KEY, NOWPAYMENTS_PUBLIC_KEY, NOWPAYMENTS_IPN_SECRET, NOWPAYMENTS_API_URL);

        // Используем точную сумму криптовалюты если она передана, иначе рассчитываем
        if ($exactCryptoAmount !== null && $exactCryptoAmount > 0) {
            $cryptoAmount = $exactCryptoAmount;
            error_log("createWithdrawalRequest INFO: Используем точную сумму из авторасчета: {$cryptoAmount} {$currency}");
        } else {
            // Получаем оценку суммы в выбранной криптовалюте
            $estimate = $api->getEstimateAmount($amount, 'usd', $currency);

            if (!isset($estimate['estimated_amount'])) {
                error_log("createWithdrawalRequest ERROR: Не удалось получить оценку суммы для конвертации {$amount} USD в {$currency}");
                return false;
            }

            $cryptoAmount = $estimate['estimated_amount'];
            error_log("createWithdrawalRequest INFO: Конвертация {$amount} USD в {$cryptoAmount} {$currency}");
        }

        // Создаем прямую выплату БЕЗ автоконвертации
        error_log("createWithdrawalRequest INFO: Создаем прямую выплату {$cryptoAmount} {$currency} для пользователя {$userId}");
        $result = $api->createSinglePayout($address, $currency, $cryptoAmount, $userId);

        // Проверяем на ошибки
        if (isset($result['error'])) {
            $errorMsg = $result['message'] ?? 'Unknown error';
            $errorCode = $result['code'] ?? 'UNKNOWN';

            // Специальная обработка ошибки недостаточного баланса
            if ($errorCode === 'BAD_CREATE_WITHDRAWAL_REQUEST' && strpos($errorMsg, 'Insufficient balance') !== false) {
                $details = $result['details']['current_values'] ?? [];
                if (isset($details[$currency])) {
                    $requested = $details[$currency]['requested'] ?? 0;
                    $available = $details[$currency]['actualBalance'] ?? 0;
                    $errorMsg = "Недостаточно средств в {$currency}. Запрошено: {$requested}, доступно: {$available}";

                    // Проверяем доступные валюты с балансом
                    $availableCurrency = $api->findCurrencyWithBalance($requested);
                    if ($availableCurrency) {
                        $altCurrency = $availableCurrency['currency'];
                        $altBalance = $availableCurrency['balance'];
                        $errorMsg .= ". Доступна валюта: {$altCurrency} (баланс: {$altBalance})";
                    } else {
                        $errorMsg .= ". Пополните баланс в панели NOWPayments";
                    }
                }
            }

            error_log("createWithdrawalRequest ERROR: {$errorMsg}");
            return ['error' => $errorMsg, 'code' => $errorCode];
        }

        if (!isset($result['id'])) {
            error_log("createWithdrawalRequest ERROR: Не удалось создать выплату. Ответ API: " . json_encode($result));
            return ['error' => 'Неожиданный ответ API'];
        }

        // Проверяем, какой тип конвертации был использован
        if (isset($result['internal_conversion'])) {
            $internalConv = $result['internal_conversion'];
            $targetCurrency = $internalConv['target_currency'];
            $targetAmount = $internalConv['target_amount'];

            error_log("createWithdrawalRequest INFO: Использована внутренняя конвертация NOWPayments: {$targetAmount} {$targetCurrency}");

            // Добавляем информацию о внутренней конвертации в результат
            $result['conversion_info'] = "Выплата создана через внутреннюю конвертацию NOWPayments в {$targetCurrency}";
        } elseif (isset($result['auto_conversion'])) {
            $autoConv = $result['auto_conversion'];
            $originalCurrency = $autoConv['original_request']['currency'];
            $actualCurrency = $autoConv['actual_payout']['currency'];
            $actualAmount = $autoConv['actual_payout']['amount'];

            error_log("createWithdrawalRequest INFO: Использована внешняя автоконвертация: {$actualAmount} {$actualCurrency} -> {$originalCurrency}");

            // Добавляем информацию об автоконвертации в результат
            $result['conversion_info'] = "Выплата создана через автоконвертацию из {$actualCurrency}";
        }

        error_log("createWithdrawalRequest SUCCESS: Создана выплата с ID: {$result['id']}");
        return $result;

    } catch (Exception $e) {
        error_log("createWithdrawalRequest ERROR: Исключение при создании выплаты: " . $e->getMessage());
        return false;
    }
}

/**
 * Получает текст статуса выплаты на русском языке
 *
 * @param string $status Статус выплаты
 * @return string Текст статуса на русском
 */
function getStatusText($status) {
    $statusMap = [
        // Статусы NOWPayments с понятными объяснениями
        'waiting' => 'Ожидание обработки',
        'processing' => 'Обрабатывается',
        'sending' => 'Отправляется на кошелек',
        'finished' => 'Отправлено на кошелек',
        'completed' => 'Отправлено на кошелек',
        'confirmed' => 'Подтверждено в блокчейне',
        'failed' => 'Ошибка выплаты',
        'rejected' => 'Отклонено системой',
        // Дополнительные статусы
        'pending' => 'В обработке',
        'cancelled' => 'Отменено',
        'expired' => 'Истекло'
    ];
    return $statusMap[$status] ?? $status ?? 'Неизвестно';
}

/**
 * Получает данные о валюте с комиссиями (синхронизировано с main.js)
 *
 * @param string $currency Код валюты (eth, btc, usdttrc20, trx)
 * @return array|null Данные о валюте или null если не найдена
 */
function getCurrencyData($currency) {
    $currencyData = [
        'ton' => [
            'name' => 'TON (Telegram)',
            'minCoins' => 1956, // Актуальный минимум из API
            'networkFee' => 0.15,
            'status' => 'best'
        ],
        'eth' => [
            'name' => 'Ethereum (ETH)',
            'minCoins' => 833, // Актуальный минимум из API
            'networkFee' => 0.25,
            'status' => 'best'
        ],
        'btc' => [
            'name' => 'Bitcoin (BTC)',
            'minCoins' => 1689, // Актуальный минимум из API
            'networkFee' => 0.50,
            'status' => 'good'
        ],
        'usdttrc20' => [
            'name' => 'USDT (TRC20)',
            'minCoins' => 21714, // Актуальный минимум из API
            'networkFee' => 5.58,
            'status' => 'expensive'
        ]
    ];

    return isset($currencyData[$currency]) ? $currencyData[$currency] : null;
}
?>
