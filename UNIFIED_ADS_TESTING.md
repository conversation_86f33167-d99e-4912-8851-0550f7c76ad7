# 🧪 Руководство по тестированию новой архитектуры рекламы

## 📋 Обзор изменений

Рекламная система была полностью рефакторена для устранения дублирования кода и создания единой, гибкой архитектуры:

### ✅ Что было улучшено:

1. **Централизованная конфигурация** - все настройки в одном месте
2. **Единый API endpoint** - вместо множества отдельных файлов
3. **Модульная архитектура** - четкое разделение ответственности
4. **Управление состоянием** - реактивная система состояния
5. **Обратная совместимость** - старый код продолжает работать

### 🗂️ Новые файлы:

- `js/ads-config.js` - Централизованная конфигурация (JS)
- `js/ads-manager-unified.js` - Единый менеджер рекламы
- `js/ads-state-manager.js` - Менеджер состояния
- `api/ads-config.php` - Централизованная конфигурация (PHP)
- `api/ads-api.php` - Единый API endpoint
- `api/migrate-to-unified-ads.php` - Скрипт миграции

## 🚀 Пошаговое тестирование

### Шаг 1: Подготовка к тестированию

```bash
# 1. Создайте резервную копию текущей системы
cp -r api/ api_backup_$(date +%Y%m%d_%H%M%S)/

# 2. Убедитесь, что все новые файлы на месте
ls -la js/ads-*.js
ls -la api/ads-*.php
```

### Шаг 2: Запуск миграции (опционально)

```bash
# Запустите скрипт миграции для обновления существующих файлов
php api/migrate-to-unified-ads.php
```

### Шаг 3: Проверка загрузки модулей

1. Откройте браузер и перейдите на сайт
2. Откройте Developer Tools (F12) → Console
3. Проверьте сообщения загрузки:

```
✅ Ожидаемые сообщения:
[AdsConfig] ✅ Конфигурация рекламы загружена и валидна
[AdsStateManager] ✅ Менеджер состояния загружен
[AdsManagerUnified] 🚀 Инициализация единого менеджера рекламы...
[AdsManagerUnified] ✅ Инициализация завершена успешно
```

### Шаг 4: Тестирование рекламных кнопок

#### 4.1 Проверка состояния кнопок
```javascript
// В консоли браузера выполните:
console.log('Состояние менеджера:', window.adsManagerUnified.getState());
console.log('Лимиты пользователя:', window.adsStateManager.getState('limits'));
```

#### 4.2 Тестирование кликов
1. Кликните по каждой рекламной кнопке:
   - "Открыть ссылку" (+10 монет)
   - "Смотреть видео" (+1 монета)
   - "Открыть рекламу" (+10 монет)

2. Проверьте в консоли:
```
✅ Ожидаемые сообщения:
[AdsManagerUnified] 🖱️ Клик по кнопке: native_banner
[AdsManagerUnified] 📺 Показ рекламы: native_banner
[AdsManagerUnified] ✅ Успешный показ рекламы: native_banner
```

#### 4.3 Проверка cooldown таймеров
1. После клика по кнопке должен запуститься 20-секундный таймер
2. Кнопка должна показывать обратный отсчет
3. Повторный клик должен быть заблокирован

### Шаг 5: Тестирование API

#### 5.1 Тест записи просмотра
```bash
curl -X POST "http://your-domain/api/ads-api.php?action=record_view" \
  -H "Content-Type: application/json" \
  -d '{
    "initData": "test_init_data",
    "adType": "native_banner"
  }'
```

#### 5.2 Тест получения лимитов
```bash
curl -X POST "http://your-domain/api/ads-api.php?action=get_limits" \
  -H "Content-Type: application/json" \
  -d '{
    "initData": "test_init_data"
  }'
```

#### 5.3 Тест логирования кликов
```bash
curl -X POST "http://your-domain/api/ads-api.php?action=log_click" \
  -H "Content-Type: application/json" \
  -d '{
    "initData": "test_init_data",
    "adType": "native_banner",
    "clickType": "button_click"
  }'
```

### Шаг 6: Проверка совместимости

#### 6.1 Тест старых API endpoints
```bash
# Старые endpoints должны работать через редирект
curl -X POST "http://your-domain/api/recordAdView.php" \
  -H "Content-Type: application/json" \
  -d '{
    "initData": "test_init_data",
    "adType": "native_banner"
  }'
```

#### 6.2 Тест legacy режима
```bash
# Добавьте ?legacy=1 для использования старого кода
curl -X POST "http://your-domain/api/recordAdView.php?legacy=1" \
  -H "Content-Type: application/json" \
  -d '{
    "initData": "test_init_data",
    "adType": "native_banner"
  }'
```

### Шаг 7: Проверка лимитов и счетчиков

1. **Дневные лимиты**: Сделайте 20+ кликов по одной кнопке
2. **Счетчики**: Проверьте обновление счетчиков под кнопками
3. **Сброс лимитов**: Проверьте сброс в 00:00 UTC

### Шаг 8: Тестирование в Telegram

1. Откройте мини-приложение в Telegram
2. Проверьте работу всех рекламных кнопок
3. Убедитесь в корректном начислении монет
4. Проверьте вибрацию и уведомления

## 🐛 Возможные проблемы и решения

### Проблема: Модули не загружаются
**Решение**: Проверьте порядок загрузки в `js/modules-loader.js`

### Проблема: RichAds SDK не инициализируется
**Решение**: 
```javascript
// Проверьте доступность SDK
console.log('TelegramGameProxy:', window.TelegramGameProxy);
// Принудительная инициализация
window.adsManagerUnified.initializeRichAds();
```

### Проблема: API возвращает ошибки
**Решение**: Проверьте логи в `api/ad_requests.log`

### Проблема: Старый код не работает
**Решение**: Используйте legacy режим или обновите вызовы

## 📊 Мониторинг и логи

### Логи JavaScript (Browser Console)
- Инициализация модулей
- Клики по кнопкам
- Ошибки API

### Логи PHP (api/ad_requests.log)
- Все API запросы
- Ошибки сервера
- Статистика использования

### Проверка состояния системы
```javascript
// Получить полное состояние системы
const state = window.adsManagerUnified.getState();
console.log('Система инициализирована:', state.isInitialized);
console.log('Реклама показывается:', state.isAdShowing);
console.log('Лимиты пользователя:', state.userLimits);
```

## ✅ Чек-лист успешного тестирования

- [ ] Все модули загружаются без ошибок
- [ ] Рекламные кнопки кликабельны
- [ ] Cooldown таймеры работают
- [ ] Счетчики обновляются
- [ ] API возвращает корректные ответы
- [ ] Монеты начисляются правильно
- [ ] Лимиты соблюдаются
- [ ] Старые API работают (совместимость)
- [ ] Telegram интеграция функционирует
- [ ] Логи записываются корректно

## 🚨 В случае проблем

1. **Откат к старой версии**:
   ```bash
   # Восстановите из резервной копии
   rm -rf api/
   mv api_backup_YYYYMMDD_HHMMSS/ api/
   ```

2. **Включение legacy режима**:
   - Добавьте `?legacy=1` к API вызовам
   - Используйте старые JavaScript файлы

3. **Отладка**:
   - Включите debug режим в `ads-config.js`
   - Проверьте логи в консоли и файлах
   - Используйте `console.log` для трассировки

## 📞 Поддержка

При возникновении проблем:
1. Соберите логи из консоли браузера
2. Проверьте `api/ad_requests.log`
3. Опишите шаги воспроизведения проблемы
4. Укажите используемый браузер и версию Telegram
