<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔒 Тест системы безопасности RichAds</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-section {
            background: #2a2a2a;
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid #444;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-button.danger:hover {
            background: #c82333;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.success:hover {
            background: #218838;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>🔒 Тест системы безопасности RichAds</h1>
    <p>Эта страница тестирует защищенную систему начисления монет</p>

    <div class="test-section">
        <h3>📊 Статистика системы безопасности</h3>
        <div id="securityStats">Загрузка...</div>
        <button class="test-button" onclick="updateSecurityStats()">🔄 Обновить статистику</button>
    </div>

    <div class="test-section">
        <h3>✅ Легальные тесты (должны работать)</h3>
        <button class="test-button success" onclick="testLegalReward('native_banner')">
            🔗 Тест баннера (легально)
        </button>
        <button class="test-button success" onclick="testLegalReward('rewarded_video')">
            📺 Тест видео (легально)
        </button>
        <button class="test-button success" onclick="testLegalReward('interstitial')">
            💰 Тест интерстишиал (легально)
        </button>
    </div>

    <div class="test-section">
        <h3>❌ Тесты накрутки (должны блокироваться)</h3>
        <button class="test-button danger" onclick="testFraudAttempt('no_token')">
            🚫 Без токена
        </button>
        <button class="test-button danger" onclick="testFraudAttempt('fake_token')">
            🎭 Поддельный токен
        </button>
        <button class="test-button danger" onclick="testFraudAttempt('old_token')">
            ⏰ Устаревший токен
        </button>
        <button class="test-button danger" onclick="testFraudAttempt('direct_api')">
            🔓 Прямой вызов API
        </button>
    </div>

    <div class="test-section">
        <h3>📝 Лог тестирования</h3>
        <div id="testLog" class="log"></div>
        <button class="test-button" onclick="clearLog()">🗑️ Очистить лог</button>
    </div>

    <div id="statusMessage"></div>

    <script src="js/richads-security.js"></script>
    <script>
        // Настройка API
        window.API_BASE_URL = 'api';
        
        // Эмуляция Telegram WebApp
        window.Telegram = {
            WebApp: {
                initData: 'user=%7B%22id%22%3A12345%2C%22first_name%22%3A%22Test%22%2C%22username%22%3A%22testuser%22%7D&chat_instance=test&chat_type=private&auth_date=1234567890&hash=test'
            }
        };

        function addToLog(message) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('statusMessage');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
        }

        function updateSecurityStats() {
            if (window.richAdsSecurityManager) {
                const stats = window.richAdsSecurityManager.getSecurityStats();
                const statsDiv = document.getElementById('securityStats');
                
                let html = `
                    <p><strong>Ожидающих наград:</strong> ${stats.pendingRewards}</p>
                    <p><strong>Ключ сессии:</strong> ${stats.securityKey}</p>
                `;
                
                if (stats.rewards.length > 0) {
                    html += '<p><strong>Активные награды:</strong></p><ul>';
                    stats.rewards.forEach(reward => {
                        html += `<li>${reward.id} - ${reward.adType} (${reward.status}, возраст: ${Math.round(reward.age/1000)}с)</li>`;
                    });
                    html += '</ul>';
                } else {
                    html += '<p><em>Нет активных наград</em></p>';
                }
                
                statsDiv.innerHTML = html;
            } else {
                document.getElementById('securityStats').innerHTML = '<p style="color: red;">❌ Система безопасности не загружена</p>';
            }
        }

        async function testLegalReward(adType) {
            addToLog(`🔄 Начинаем легальный тест для ${adType}`);
            
            try {
                if (!window.richAdsSecurityManager) {
                    throw new Error('Система безопасности не загружена');
                }
                
                // 1. Регистрируем ожидающую награду (как при клике на кнопку)
                const rewardData = window.richAdsSecurityManager.registerPendingReward(12345, adType);
                addToLog(`✅ Зарегистрирована награда: ${rewardData.rewardId}`);
                
                // 2. Эмулируем успешный показ RichAds (через 1 секунду)
                setTimeout(async () => {
                    try {
                        const result = await window.richAdsSecurityManager.confirmReward(12345, adType, {
                            success: true,
                            timestamp: Date.now(),
                            source: 'test_legal'
                        });
                        
                        addToLog(`🎉 Награда успешно начислена: ${result.reward} монет, баланс: ${result.newBalance}`);
                        showStatus(`✅ Легальный тест ${adType} прошел успешно!`, 'success');
                        updateSecurityStats();
                        
                    } catch (error) {
                        addToLog(`❌ Ошибка подтверждения: ${error.message}`);
                        showStatus(`❌ Ошибка в легальном тесте: ${error.message}`, 'error');
                    }
                }, 1000);
                
            } catch (error) {
                addToLog(`❌ Ошибка легального теста: ${error.message}`);
                showStatus(`❌ Ошибка: ${error.message}`, 'error');
            }
        }

        async function testFraudAttempt(type) {
            addToLog(`🚫 Тестируем попытку накрутки: ${type}`);
            
            try {
                let requestData = {
                    initData: window.Telegram.WebApp.initData,
                    adType: 'native_banner',
                    timestamp: Date.now()
                };
                
                switch (type) {
                    case 'no_token':
                        // Не добавляем токен вообще
                        break;
                        
                    case 'fake_token':
                        requestData.richAdsToken = 'fake_token_12345_native_banner_' + Date.now();
                        break;
                        
                    case 'old_token':
                        // Создаем токен с устаревшей временной меткой
                        const oldTimestamp = Math.floor(Date.now() / 1000) - 600; // 10 минут назад
                        requestData.richAdsToken = btoa(`12345_native_banner_${oldTimestamp}_test_old`);
                        break;
                        
                    case 'direct_api':
                        // Прямой вызов старого API
                        const response = await fetch('api/recordAdView.php?legacy=1', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                initData: requestData.initData,
                                adType: 'native_banner'
                            })
                        });
                        
                        if (response.ok) {
                            const data = await response.json();
                            addToLog(`⚠️ УЯЗВИМОСТЬ: Прямой API сработал! Баланс: ${data.newBalance}`);
                            showStatus('⚠️ НАЙДЕНА УЯЗВИМОСТЬ в прямом API!', 'warning');
                        } else {
                            addToLog(`✅ Прямой API заблокирован: ${response.status}`);
                        }
                        return;
                }
                
                // Отправляем запрос на защищенный API
                const response = await fetch('api/secure_reward.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(requestData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    addToLog(`⚠️ УЯЗВИМОСТЬ: Накрутка ${type} прошла! Баланс: ${data.newBalance}`);
                    showStatus(`⚠️ НАЙДЕНА УЯЗВИМОСТЬ: ${type}`, 'warning');
                } else {
                    addToLog(`✅ Накрутка ${type} заблокирована: ${data.error}`);
                    showStatus(`✅ Накрутка ${type} успешно заблокирована`, 'success');
                }
                
            } catch (error) {
                addToLog(`✅ Накрутка ${type} заблокирована (ошибка): ${error.message}`);
                showStatus(`✅ Накрутка ${type} заблокирована`, 'success');
            }
        }

        // Инициализация при загрузке
        window.addEventListener('load', () => {
            addToLog('🔒 Система тестирования безопасности загружена');
            updateSecurityStats();
            
            // Обновляем статистику каждые 5 секунд
            setInterval(updateSecurityStats, 5000);
        });
    </script>
</body>
</html>
